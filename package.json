{"name": "cloudfront", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "start:dev": "nodemon server.js"}, "type": "module", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.891.0", "@aws-sdk/cloudfront-signer": "^3.887.0", "dotenv": "^17.2.2", "express": "^5.1.0", "multer": "^2.0.2", "nodemon": "^3.1.10"}, "author": "", "license": "ISC", "description": ""}