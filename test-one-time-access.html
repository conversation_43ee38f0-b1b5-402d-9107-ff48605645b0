<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>One-Time Access CloudFront Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input[type="file"], input[type="text"], input[type="number"] {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .token-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>🔒 One-Time Access CloudFront Test</h1>
    
    <div class="container">
        <h2>📤 Upload File</h2>
        <form id="uploadForm">
            <input type="file" id="fileInput" required>
            <br>
            <button type="submit">Upload File & Get One-Time URL</button>
        </form>
        <div id="uploadResult"></div>
    </div>

    <div class="container">
        <h2>🔗 Generate One-Time Access URL</h2>
        <input type="text" id="filePathInput" placeholder="Enter file path (e.g., /test-file.pdf)" style="width: 300px;">
        <input type="number" id="expiresInput" placeholder="Expires in seconds" value="60" style="width: 150px;">
        <button onclick="generateOneTimeUrl()">Generate One-Time URL</button>
        <div id="urlResult"></div>
    </div>

    <div class="container">
        <h2>🔍 Token Preview</h2>
        <input type="text" id="tokenInput" placeholder="Enter token to preview" style="width: 400px;">
        <button onclick="previewToken()">Preview Token</button>
        <div id="previewResult"></div>
    </div>

    <div class="container">
        <h2>📊 System Statistics</h2>
        <button onclick="getStats()">Get Token Statistics</button>
        <button onclick="cleanupTokens()">Cleanup Expired Tokens</button>
        <button onclick="checkHealth()">Check System Health</button>
        <div id="statsResult"></div>
    </div>

    <div class="container">
        <h2>🧪 Test One-Time Access</h2>
        <div class="info">
            <strong>Instructions:</strong>
            <ol>
                <li>Upload a file or generate a one-time URL</li>
                <li>Copy the one-time access URL</li>
                <li>Click it once - it should work</li>
                <li>Try clicking it again - it should be blocked</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="result error">Please select a file</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            try {
                resultDiv.innerHTML = '<div class="result info">Uploading...</div>';
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>✅ Upload Successful!</strong><br>
                            <strong>File Key:</strong> ${result.key}<br>
                            <strong>S3 URL:</strong> <a href="${result.s3Url}" target="_blank">${result.s3Url}</a>
                        </div>
                        <div class="token-info">
                            <strong>🔒 One-Time Access:</strong><br>
                            <strong>URL:</strong> <a href="${result.oneTimeAccess.url}" target="_blank">${result.oneTimeAccess.url}</a><br>
                            <strong>Token:</strong> ${result.oneTimeAccess.token}<br>
                            <strong>Expires:</strong> ${result.oneTimeAccess.expiresAt}<br>
                            <div class="warning">⚠️ This URL can only be accessed ONCE!</div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        });
        
        async function generateOneTimeUrl() {
            const filePathInput = document.getElementById('filePathInput');
            const expiresInput = document.getElementById('expiresInput');
            const resultDiv = document.getElementById('urlResult');
            
            if (!filePathInput.value) {
                resultDiv.innerHTML = '<div class="result error">Please enter a file path</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="result info">Generating one-time URL...</div>';
                
                const expires = expiresInput.value || 60;
                const response = await fetch(`/get-one-time-url?file=${encodeURIComponent(filePathInput.value)}&expires=${expires}`);
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>✅ One-Time URL Generated!</strong><br>
                            <strong>Access URL:</strong> <a href="${result.oneTimeUrl}" target="_blank">${result.oneTimeUrl}</a><br>
                            <strong>Token:</strong> ${result.token}<br>
                            <strong>Expires:</strong> ${result.expiresAt}
                        </div>
                        <div class="token-info">
                            <strong>Available Actions:</strong><br>
                            <button onclick="window.open('${result.accessInstructions.access}', '_blank')">🔗 Access File</button>
                            <button onclick="window.open('${result.accessInstructions.preview}', '_blank')">👁️ Preview Token</button>
                            <button onclick="window.open('${result.accessInstructions.download}', '_blank')">💾 Download File</button>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        async function previewToken() {
            const tokenInput = document.getElementById('tokenInput');
            const resultDiv = document.getElementById('previewResult');
            
            if (!tokenInput.value) {
                resultDiv.innerHTML = '<div class="result error">Please enter a token</div>';
                return;
            }
            
            try {
                const response = await fetch(`/preview/${tokenInput.value}`);
                const result = await response.json();
                
                if (response.ok) {
                    const timeRemaining = result.timeRemaining;
                    const status = result.used ? '❌ Used' : (timeRemaining > 0 ? '✅ Valid' : '⏰ Expired');
                    
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>Token Status: ${status}</strong><br>
                            <strong>File Path:</strong> ${result.filePath}<br>
                            <strong>Created:</strong> ${result.createdAt}<br>
                            <strong>Expires:</strong> ${result.expiresAt}<br>
                            <strong>Time Remaining:</strong> ${timeRemaining} seconds<br>
                            <strong>Used:</strong> ${result.used ? 'Yes' : 'No'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        async function getStats() {
            const resultDiv = document.getElementById('statsResult');
            
            try {
                const response = await fetch('/admin/stats');
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>📊 Token Statistics</strong>
                            <div class="stats">
                                <div class="stat-item">
                                    <strong>${result.totalTokens}</strong><br>Total Tokens
                                </div>
                                <div class="stat-item">
                                    <strong>${result.activeTokens}</strong><br>Active Tokens
                                </div>
                                <div class="stat-item">
                                    <strong>${result.usedTokens}</strong><br>Used Tokens
                                </div>
                                <div class="stat-item">
                                    <strong>${result.expiredTokens}</strong><br>Expired Tokens
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        async function cleanupTokens() {
            const resultDiv = document.getElementById('statsResult');
            
            try {
                const response = await fetch('/admin/cleanup', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>🧹 Cleanup Completed</strong><br>
                            Cleaned up ${result.cleanedTokens} expired tokens
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        async function checkHealth() {
            const resultDiv = document.getElementById('statsResult');
            
            try {
                const response = await fetch('/health');
                const result = await response.json();
                
                const statusColor = result.status === 'healthy' ? 'result' : 'result error';
                resultDiv.innerHTML = `
                    <div class="${statusColor}">
                        <strong>🏥 System Health: ${result.status.toUpperCase()}</strong><br>
                        <strong>Redis:</strong> ${result.redis}<br>
                        <strong>Timestamp:</strong> ${result.timestamp}
                        ${result.error ? `<br><strong>Error:</strong> ${result.error}` : ''}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        // Auto-refresh stats every 30 seconds
        setInterval(() => {
            const statsDiv = document.getElementById('statsResult');
            if (statsDiv.innerHTML.includes('Token Statistics')) {
                getStats();
            }
        }, 30000);
    </script>
</body>
</html>
