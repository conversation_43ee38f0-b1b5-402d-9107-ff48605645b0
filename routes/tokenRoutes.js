import express from 'express';
import { validateToken, checkToken, logTokenOperation } from '../middleware/tokenValidation.js';
import tokenManager from '../tokenManager.js';

const router = express.Router();

/**
 * Access endpoint - validates token and redirects to CloudFront URL
 * This is the main endpoint that users will access with their one-time tokens
 */
router.get('/access/:token', 
    logTokenOperation('access'),
    validateToken,
    async (req, res) => {
        try {
            const { tokenData } = req;
            
            // Log the access for security/audit purposes
            console.log(`One-time access granted for file: ${tokenData.filePath} at ${new Date().toISOString()}`);
            
            // Redirect to the actual CloudFront signed URL
            res.redirect(302, tokenData.signedUrl);
            
        } catch (error) {
            console.error('Error in access endpoint:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to process access request'
            });
        }
    }
);

/**
 * Preview endpoint - checks token validity without consuming it
 * Useful for showing file info before actual access
 */
router.get('/preview/:token',
    logTokenOperation('preview'),
    checkToken,
    async (req, res) => {
        try {
            const { tokenData } = req;
            
            res.json({
                valid: true,
                filePath: tokenData.filePath,
                createdAt: new Date(tokenData.createdAt).toISOString(),
                expiresAt: new Date(tokenData.expiresAt).toISOString(),
                timeRemaining: Math.max(0, Math.floor((tokenData.expiresAt - Date.now()) / 1000)),
                used: tokenData.used
            });
            
        } catch (error) {
            console.error('Error in preview endpoint:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to preview token'
            });
        }
    }
);

/**
 * Download endpoint - validates token and serves file with proper headers
 * Alternative to redirect, serves the file directly through our server
 */
router.get('/download/:token',
    logTokenOperation('download'),
    validateToken,
    async (req, res) => {
        try {
            const { tokenData } = req;
            
            // Log the download for security/audit purposes
            console.log(`One-time download initiated for file: ${tokenData.filePath} at ${new Date().toISOString()}`);
            
            // Fetch the file from CloudFront and stream it to the client
            const fetch = (await import('node-fetch')).default;
            const response = await fetch(tokenData.signedUrl);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
            }
            
            // Set appropriate headers
            const filename = tokenData.filePath.split('/').pop();
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
            res.setHeader('Content-Type', response.headers.get('content-type') || 'application/octet-stream');
            
            if (response.headers.get('content-length')) {
                res.setHeader('Content-Length', response.headers.get('content-length'));
            }
            
            // Stream the file
            response.body.pipe(res);
            
        } catch (error) {
            console.error('Error in download endpoint:', error);
            res.status(500).json({
                error: 'Internal server error',
                message: 'Failed to download file'
            });
        }
    }
);

/**
 * Admin endpoint - get token statistics
 */
router.get('/admin/stats', async (req, res) => {
    try {
        const stats = await tokenManager.getTokenStats();
        res.json(stats);
    } catch (error) {
        console.error('Error getting token stats:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to get token statistics'
        });
    }
});

/**
 * Admin endpoint - cleanup expired tokens
 */
router.post('/admin/cleanup', async (req, res) => {
    try {
        const cleanedCount = await tokenManager.cleanupExpiredTokens();
        res.json({
            message: 'Cleanup completed',
            cleanedTokens: cleanedCount
        });
    } catch (error) {
        console.error('Error cleaning up tokens:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: 'Failed to cleanup tokens'
        });
    }
});

/**
 * Health check for token system
 */
router.get('/health', async (req, res) => {
    try {
        await tokenManager.connect();
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            redis: 'connected'
        });
    } catch (error) {
        res.status(503).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            redis: 'disconnected',
            error: error.message
        });
    }
});

export default router;
