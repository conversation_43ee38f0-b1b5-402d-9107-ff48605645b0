import { createClient } from 'redis';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

class TokenManager {
    constructor() {
        this.client = null;
        this.isConnected = false;
    }

    async connect() {
        if (this.isConnected) return;

        try {
            this.client = createClient({
                url: process.env.REDIS_URL || 'redis://localhost:6379'
            });

            this.client.on('error', (err) => {
                console.error('Redis Client Error:', err);
            });

            this.client.on('connect', () => {
                console.log('Connected to Redis');
            });

            await this.client.connect();
            this.isConnected = true;
        } catch (error) {
            console.error('Failed to connect to Redis:', error);
            throw error;
        }
    }

    async disconnect() {
        if (this.client && this.isConnected) {
            await this.client.disconnect();
            this.isConnected = false;
        }
    }

    /**
     * Generate a unique one-time token
     * @param {string} filePath - The file path for the CloudFront URL
     * @param {number} expiresIn - Expiration time in seconds
     * @returns {string} Unique token
     */
    generateToken(filePath, expiresIn = 60) {
        const timestamp = Date.now();
        const randomBytes = crypto.randomBytes(16).toString('hex');
        const data = `${filePath}:${timestamp}:${randomBytes}`;
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * Store a token with its metadata
     * @param {string} token - The token to store
     * @param {string} filePath - The file path
     * @param {string} signedUrl - The actual CloudFront signed URL
     * @param {number} expiresIn - Expiration time in seconds
     */
    async storeToken(token, filePath, signedUrl, expiresIn = 60) {
        await this.connect();
        
        const tokenData = {
            filePath,
            signedUrl,
            createdAt: Date.now(),
            used: false,
            expiresAt: Date.now() + (expiresIn * 1000)
        };

        // Store token with expiration
        await this.client.setEx(
            `token:${token}`, 
            expiresIn, 
            JSON.stringify(tokenData)
        );

        console.log(`Token stored: ${token} for file: ${filePath}`);
    }

    /**
     * Validate and consume a token (mark as used)
     * @param {string} token - The token to validate
     * @returns {Object|null} Token data if valid, null if invalid/used/expired
     */
    async validateAndConsumeToken(token) {
        await this.connect();

        try {
            const tokenDataStr = await this.client.get(`token:${token}`);
            
            if (!tokenDataStr) {
                console.log(`Token not found or expired: ${token}`);
                return null;
            }

            const tokenData = JSON.parse(tokenDataStr);

            // Check if token is already used
            if (tokenData.used) {
                console.log(`Token already used: ${token}`);
                return null;
            }

            // Check if token is expired
            if (Date.now() > tokenData.expiresAt) {
                console.log(`Token expired: ${token}`);
                await this.client.del(`token:${token}`);
                return null;
            }

            // Mark token as used
            tokenData.used = true;
            tokenData.usedAt = Date.now();

            // Update the token in Redis (keep it for a short while for logging/debugging)
            await this.client.setEx(
                `token:${token}`, 
                300, // Keep used tokens for 5 minutes
                JSON.stringify(tokenData)
            );

            console.log(`Token consumed: ${token} for file: ${tokenData.filePath}`);
            return tokenData;

        } catch (error) {
            console.error('Error validating token:', error);
            return null;
        }
    }

    /**
     * Check if a token exists and is valid (without consuming it)
     * @param {string} token - The token to check
     * @returns {Object|null} Token data if valid, null if invalid/used/expired
     */
    async checkToken(token) {
        await this.connect();

        try {
            const tokenDataStr = await this.client.get(`token:${token}`);
            
            if (!tokenDataStr) {
                return null;
            }

            const tokenData = JSON.parse(tokenDataStr);

            if (tokenData.used || Date.now() > tokenData.expiresAt) {
                return null;
            }

            return tokenData;
        } catch (error) {
            console.error('Error checking token:', error);
            return null;
        }
    }

    /**
     * Clean up expired tokens (can be called periodically)
     */
    async cleanupExpiredTokens() {
        await this.connect();
        
        try {
            const keys = await this.client.keys('token:*');
            let cleanedCount = 0;

            for (const key of keys) {
                const tokenDataStr = await this.client.get(key);
                if (tokenDataStr) {
                    const tokenData = JSON.parse(tokenDataStr);
                    if (Date.now() > tokenData.expiresAt) {
                        await this.client.del(key);
                        cleanedCount++;
                    }
                }
            }

            console.log(`Cleaned up ${cleanedCount} expired tokens`);
            return cleanedCount;
        } catch (error) {
            console.error('Error cleaning up tokens:', error);
            return 0;
        }
    }

    /**
     * Get token statistics
     */
    async getTokenStats() {
        await this.connect();
        
        try {
            const keys = await this.client.keys('token:*');
            let totalTokens = 0;
            let usedTokens = 0;
            let expiredTokens = 0;
            let activeTokens = 0;

            for (const key of keys) {
                const tokenDataStr = await this.client.get(key);
                if (tokenDataStr) {
                    totalTokens++;
                    const tokenData = JSON.parse(tokenDataStr);
                    
                    if (tokenData.used) {
                        usedTokens++;
                    } else if (Date.now() > tokenData.expiresAt) {
                        expiredTokens++;
                    } else {
                        activeTokens++;
                    }
                }
            }

            return {
                totalTokens,
                usedTokens,
                expiredTokens,
                activeTokens
            };
        } catch (error) {
            console.error('Error getting token stats:', error);
            return null;
        }
    }
}

// Create singleton instance
const tokenManager = new TokenManager();

export default tokenManager;
