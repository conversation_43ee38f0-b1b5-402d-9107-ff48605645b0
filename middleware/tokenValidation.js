import tokenManager from '../tokenManager.js';

/**
 * Middleware to validate one-time access tokens
 */
export const validateToken = async (req, res, next) => {
    try {
        const { token } = req.params;

        if (!token) {
            return res.status(400).json({ 
                error: 'Token is required',
                message: 'No access token provided'
            });
        }

        // Validate and consume the token
        const tokenData = await tokenManager.validateAndConsumeToken(token);

        if (!tokenData) {
            return res.status(403).json({ 
                error: 'Invalid or expired token',
                message: 'The access token is invalid, expired, or has already been used'
            });
        }

        // Attach token data to request for use in route handler
        req.tokenData = tokenData;
        next();

    } catch (error) {
        console.error('Token validation error:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: 'Failed to validate token'
        });
    }
};

/**
 * Middleware to check token without consuming it (for preview/info endpoints)
 */
export const checkToken = async (req, res, next) => {
    try {
        const { token } = req.params;

        if (!token) {
            return res.status(400).json({ 
                error: 'Token is required',
                message: 'No access token provided'
            });
        }

        // Check token without consuming it
        const tokenData = await tokenManager.checkToken(token);

        if (!tokenData) {
            return res.status(403).json({ 
                error: 'Invalid or expired token',
                message: 'The access token is invalid, expired, or has already been used'
            });
        }

        // Attach token data to request for use in route handler
        req.tokenData = tokenData;
        next();

    } catch (error) {
        console.error('Token check error:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: 'Failed to check token'
        });
    }
};

/**
 * Rate limiting middleware for token generation
 */
export const rateLimitTokenGeneration = (maxRequests = 10, windowMs = 60000) => {
    const requests = new Map();

    return (req, res, next) => {
        const clientId = req.ip || req.connection.remoteAddress;
        const now = Date.now();
        
        // Clean up old entries
        for (const [id, data] of requests.entries()) {
            if (now - data.firstRequest > windowMs) {
                requests.delete(id);
            }
        }

        // Check current client
        const clientData = requests.get(clientId);
        
        if (!clientData) {
            requests.set(clientId, {
                count: 1,
                firstRequest: now
            });
            return next();
        }

        if (now - clientData.firstRequest > windowMs) {
            // Reset window
            requests.set(clientId, {
                count: 1,
                firstRequest: now
            });
            return next();
        }

        if (clientData.count >= maxRequests) {
            return res.status(429).json({
                error: 'Too many requests',
                message: `Maximum ${maxRequests} token requests per minute exceeded`
            });
        }

        clientData.count++;
        next();
    };
};

/**
 * Logging middleware for token operations
 */
export const logTokenOperation = (operation) => {
    return (req, res, next) => {
        const startTime = Date.now();
        
        // Log request
        console.log(`[${new Date().toISOString()}] Token ${operation} - IP: ${req.ip}, Token: ${req.params.token || 'N/A'}`);
        
        // Override res.json to log response
        const originalJson = res.json;
        res.json = function(data) {
            const duration = Date.now() - startTime;
            console.log(`[${new Date().toISOString()}] Token ${operation} completed - Status: ${res.statusCode}, Duration: ${duration}ms`);
            return originalJson.call(this, data);
        };

        next();
    };
};

export default {
    validateToken,
    checkToken,
    rateLimitTokenGeneration,
    logTokenOperation
};
