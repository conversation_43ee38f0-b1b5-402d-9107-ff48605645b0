<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudFront S3 Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>CloudFront S3 Upload Test</h1>
    
    <div class="container">
        <h2>Upload File</h2>
        <form id="uploadForm">
            <input type="file" id="fileInput" required>
            <br>
            <button type="submit">Upload File</button>
        </form>
        <div id="uploadResult"></div>
    </div>

    <div class="container">
        <h2>Get Signed URL</h2>
        <input type="text" id="filePathInput" placeholder="Enter file path (e.g., /mydocs/file.pdf)" style="width: 300px;">
        <button onclick="getSignedUrl()">Get Signed URL</button>
        <div id="urlResult"></div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<div class="result error">Please select a file</div>';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            try {
                resultDiv.innerHTML = '<div class="result">Uploading...</div>';
                
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>Upload Successful!</strong><br>
                            <strong>File Key:</strong> ${result.key}<br>
                            <strong>S3 URL:</strong> <a href="${result.s3Url}" target="_blank">${result.s3Url}</a>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        });
        
        async function getSignedUrl() {
            const filePathInput = document.getElementById('filePathInput');
            const resultDiv = document.getElementById('urlResult');
            
            if (!filePathInput.value) {
                resultDiv.innerHTML = '<div class="result error">Please enter a file path</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="result">Generating signed URL...</div>';
                
                const response = await fetch(`/get-signed-url?file=${encodeURIComponent(filePathInput.value)}`);
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <strong>Signed URL Generated!</strong><br>
                            <a href="${result.signedUrl}" target="_blank">${result.signedUrl}</a>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
